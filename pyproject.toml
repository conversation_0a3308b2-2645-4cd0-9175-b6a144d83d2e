[tool.poetry]
name = "ecourt-fast-app"
version = "0.1.0"
description = ""
authors = ["amardaxini <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.115.12"
sqlalchemy = "^2.0.40"
psycopg2-binary = "^2.9.10"
alembic = "^1.15.2"
celery = "^5.5.1"
redis = "^5.2.1"
pydantic = "^2.11.3"
pydantic-settings = "^2.8.1"
python-dotenv = "^1.1.0"
httpx = "^0.28.1"
asyncpg = "^0.30.0"
sqlalchemy-utils = "^0.41.2"
psycopg = "^3.2.6"
requests = "^2.32.3"
pycryptodome = "^3.22.0"
uvicorn = {extras = ["standard"], version = "^0.34.0"}
greenlet = "^3.1.1"
ipython = "^9.1.0"
beautifulsoup4 = "^4.13.3"
flower = "^2.0.1"
gevent = "^24.11.1"


[tool.poetry.group.dev.dependencies]
ipython = "^9.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
