"""Add courts table

Revision ID: b73c11731ce3
Revises: 64f279cba190
Create Date: 2025-05-20 17:04:48.039016

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b73c11731ce3'
down_revision: Union[str, None] = '64f279cba190'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('courts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('court_name', sa.String(), nullable=False),
    sa.Column('complex_code', sa.Integer(), nullable=True),
    sa.Column('court_complex_name', sa.String(), nullable=True),
    sa.Column('court_code', sa.Integer(), nullable=True),
    sa.Column('national_court_code', sa.String(), nullable=True),
    sa.Column('district_code', sa.Integer(), nullable=True),
    sa.Column('dist_name', sa.String(), nullable=True),
    sa.Column('state_code', sa.Integer(), nullable=True),
    sa.Column('state_name', sa.String(), nullable=True),
    sa.Column('nationalstate_code', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('national_court_code')
    )
    op.create_index(op.f('ix_courts_id'), 'courts', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_courts_id'), table_name='courts')
    op.drop_table('courts')
    # ### end Alembic commands ###
