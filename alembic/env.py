# alembic/env.py
import os
import sys
from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

# --- Add this block to ensure the app directory is in the Python path ---
# Adjust the path ('../') if your alembic.ini is not in the project root alongside the 'app' folder
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)
# --- End Path Block ---

# Import Base from your database setup
from app.database import Base

# --- Import your models HERE so Alembic detects them ---
# Make sure this import happens *after* Base is defined/imported
# and *after* the sys.path modification if needed.
# Ensure app/models/__init__.py also imports the necessary model files/classes.
# Explicitly importing the modules containing models is often safer.
from app.models import Case, ApiToken

# --- End Model Import ---

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# target_metadata = mymodel.Base.metadata
# --- Set target_metadata AFTER models are imported ---
target_metadata = Base.metadata
# --- End Set ---

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    # Load environment variables for DB URL
    from dotenv import load_dotenv

    load_dotenv()
    url = os.getenv("DATABASE_URL", config.get_main_option("sqlalchemy.url"))
    if not url:
        raise ValueError(
            "Database URL not found in environment variables or alembic.ini for offline mode"
        )

    context.configure(
        url=url,
        target_metadata=target_metadata,  # Ensure this uses the correct metadata
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    # Load environment variables for DB URL
    from dotenv import load_dotenv

    load_dotenv()

    # Get DB URL from environment, fallback to alembic.ini
    db_url = os.getenv(
        "DATABASE_URL",
        config.get_section(config.config_ini_section).get("sqlalchemy.url"),
    )
    if not db_url:
        raise ValueError(
            "Database URL not found in environment variables or alembic.ini for online mode"
        )

    # Create engine configuration dictionary, ensuring the correct URL from .env is used
    connectable_config = config.get_section(config.config_ini_section) or {}
    connectable_config["sqlalchemy.url"] = db_url

    connectable = engine_from_config(
        connectable_config,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
        future=True,  # Ensure engine uses 2.0 style if your main app does
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,  # Ensure this uses the correct metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
