# scripts/seed_tokens.py
import redis
import argparse
import logging
import sys
import os
import asyncio

# --- sys.path modification block REMOVED ---

# Try importing directly - poetry run should make 'app' available
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

try:
    from app.config import settings
    from app.database import AsyncSessionLocal
    from app import schemas
    from app.repositories import token_repository

    try:
        # Use the function name without the leading underscore
        from app.utils.api_client import (
            fetch_new_ecourts_token_sync,
            fetch_token_with_uuid,
        )
    except ImportError:
        print("ERROR: Could not import token functions from 'app.utils.api_client'.")
        print("Ensure:")
        print("  1. The file 'app/utils/api_client.py' exists.")
        print("  2. The required functions are defined correctly within that file.")
        print(
            "  3. You are running this script using 'poetry run python scripts/seed_token.py' from the project root."
        )
        sys.exit(1)
except ImportError as e:
    print(f"ERROR: Could not import application modules: {e}")
    print(
        "Make sure you run this script using 'poetry run python scripts/seed_token.py' from the project root directory."
    )
    sys.exit(1)

# Basic Logging Setup
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def seed_tokens_async(num_tokens_to_generate: int):
    """Fetches the specified number of tokens and adds them to the database and Redis pool."""
    redis_key = settings.ECOURTS_API_TOKEN_POOL_KEY
    tokens_added_to_redis = 0
    tokens_added_to_db = 0
    r = None
    db = None

    logger.info(
        f"Starting token seeding. Goal: Generate {num_tokens_to_generate} tokens."
    )

    try:
        # Connect to Redis for backward compatibility
        logger.info("Connecting to Redis...")
        r = redis.from_url(settings.REDIS_URL, decode_responses=True)
        r.ping()
        logger.info("Redis connection successful.")

        # Connect to database
        logger.info("Connecting to database...")
        db = AsyncSessionLocal()

        # Get existing UUIDs from database to reuse them
        existing_tokens = await token_repository.get_all_tokens(db)
        existing_uuids = [token.uuid for token in existing_tokens if token.uuid]

        # If we have fewer existing UUIDs than requested count, we'll generate new ones
        uuids_to_use = existing_uuids[:num_tokens_to_generate]
        new_uuids_needed = max(0, num_tokens_to_generate - len(uuids_to_use))

        logger.info(
            f"Found {len(existing_uuids)} existing UUIDs, will reuse {len(uuids_to_use)} and generate {new_uuids_needed} new ones"
        )

        # First, try to regenerate tokens for existing UUIDs
        for i, uuid_str in enumerate(uuids_to_use):
            try:
                logger.info(
                    f"Regenerating token for existing UUID {i+1}/{len(uuids_to_use)}: {uuid_str}"
                )
                uuid_str, new_token = await fetch_token_with_uuid(uuid_str)

                # Store in database
                token_data = schemas.TokenUpdate(
                    token=new_token, is_active=True, last_used_at=None
                )
                existing_token = await token_repository.get_token_by_uuid(db, uuid_str)
                await token_repository.update_token(db, existing_token.id, token_data)
                tokens_added_to_db += 1

                # Also add to Redis for backward compatibility
                result = r.sadd(redis_key, new_token)
                if result == 1:
                    tokens_added_to_redis += 1
                    logger.info(
                        f"Successfully regenerated token for UUID {uuid_str} and added to Redis"
                    )
                else:
                    logger.warning(
                        f"Token for UUID {uuid_str} was already present in Redis pool (not added again)"
                    )
            except Exception as token_error:
                logger.error(
                    f"Failed to regenerate token for UUID {uuid_str}: {token_error}"
                )
                # Mark this token as inactive
                try:
                    await token_repository.deactivate_token(db, uuid_str)
                    logger.info(f"Marked token with UUID {uuid_str} as inactive")
                except Exception as e:
                    logger.error(f"Failed to mark token as inactive: {e}")
                continue

        # Then generate completely new tokens if needed
        for i in range(new_uuids_needed):
            try:
                logger.info(f"Generating new token {i+1}/{new_uuids_needed}")
                uuid_str, new_token = await fetch_token_with_uuid()

                # Store in database
                token_data = schemas.TokenCreate(
                    uuid=uuid_str, token=new_token, is_active=True
                )
                await token_repository.create_token(db, token_data)
                tokens_added_to_db += 1

                # Also add to Redis for backward compatibility
                result = r.sadd(redis_key, new_token)
                if result == 1:
                    tokens_added_to_redis += 1
                    logger.info(
                        f"Successfully generated new token with UUID {uuid_str} and added to Redis"
                    )
                else:
                    logger.warning(
                        f"New token with UUID {uuid_str} was already present in Redis pool (not added again)"
                    )
            except Exception as token_error:
                logger.error(f"Failed to generate new token: {token_error}")
                continue

    except redis.RedisError as e:
        logger.error(f"Redis Error during seeding process: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during seeding: {e}")
    finally:
        if r:
            try:
                final_size = r.scard(redis_key)
                logger.info(
                    f"Final Redis token pool size ('{redis_key}'): {final_size}"
                )
                r.close()
                logger.info("Redis connection closed.")
            except Exception as close_err:
                logger.error(
                    f"Error checking pool size or closing Redis connection: {close_err}"
                )
        if db:
            try:
                await db.close()
                logger.info("Database connection closed.")
            except Exception as close_err:
                logger.error(f"Error closing database connection: {close_err}")

        logger.info(
            f"--- Seeding process finished. Added {tokens_added_to_db} tokens to DB and {tokens_added_to_redis} to Redis. ---"
        )

    return tokens_added_to_db, tokens_added_to_redis


def seed_tokens(num_tokens_to_generate: int):
    """Wrapper function to run the async seed_tokens_async function."""
    return asyncio.run(seed_tokens_async(num_tokens_to_generate))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Seed the Redis pool with new eCourts API tokens."
    )
    parser.add_argument(
        "-c",
        "--count",
        type=int,
        default=settings.ECOURTS_TOKENS_TO_GENERATE_PER_RUN,
        help=f"Number of tokens to generate (default: {settings.ECOURTS_TOKENS_TO_GENERATE_PER_RUN} from settings)",
    )
    args = parser.parse_args()
    if args.count <= 0:
        logger.warning("Token count must be positive. Exiting.")
    else:
        seed_tokens(args.count)
