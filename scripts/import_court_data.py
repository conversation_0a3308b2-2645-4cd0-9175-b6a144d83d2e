# scripts/import_court_data_standalone.py
import asyncio
import json
import os
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import Column, Integer, String, DateTime, Boolean, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import dotenv

# Load environment variables
dotenv.load_dotenv()

# Database connection
DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_async_engine(DATABASE_URL)
AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

# Define models
Base = declarative_base()


class Court(Base):
    __tablename__ = "courts"

    id = Column(Integer, primary_key=True, index=True)
    court_name = Column(String, nullable=False)
    complex_code = Column(Integer, nullable=True)
    court_complex_name = Column(String, nullable=True)
    court_code = Column(Integer, nullable=True)
    national_court_code = Column(String, nullable=True, unique=True)
    district_code = Column(Integer, nullable=True)
    dist_name = Column(String, nullable=True)
    state_code = Column(Integer, nullable=True)
    state_name = Column(String, nullable=True)
    nationalstate_code = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )


async def import_courts():
    # Load the JSON data
    with open("scripts/master_data.json", "r") as f:
        master_data = json.load(f)

    # Create an async session
    async with AsyncSessionLocal() as session:
        # Process each state
        for state in master_data:
            state_code = state["state_code"]
            state_name = state["state_name"]
            nationalstate_code = state["nationalstate_code"]

            # Process each district in the state
            for district in state["districts"]:
                district_code = district["district_code"]
                dist_name = district["dist_name"]

                # Process each court in the district
                for court in district["courts"]:
                    # Skip if the court doesn't have a national_court_code
                    if "national_court_code" not in court:
                        print(
                            f"Skipping court without national_court_code: {court.get('court_name', 'Unknown')}"
                        )
                        continue

                    # Check if court already exists
                    query = text(
                        "SELECT id FROM courts WHERE national_court_code = :code"
                    )
                    result = await session.execute(
                        query, {"code": court["national_court_code"]}
                    )
                    existing_court = result.scalar_one_or_none()

                    if not existing_court:
                        # Create court object with safe access to fields
                        db_court = Court(
                            court_name=court.get("court_name", ""),
                            complex_code=court.get("complex_code"),
                            court_complex_name=court.get("court_complex_name"),
                            court_code=court.get("court_code"),
                            national_court_code=court[
                                "national_court_code"
                            ],  # This is guaranteed to exist from the check above
                            district_code=district_code,
                            dist_name=dist_name,
                            state_code=state_code,
                            state_name=state_name,
                            nationalstate_code=nationalstate_code,
                            is_active=True,
                        )

                        # Save to database
                        session.add(db_court)
                        await session.commit()
                        print(f"Imported court: {court['court_name']}")
                    else:
                        print(f"Court already exists: {court['court_name']}")


if __name__ == "__main__":
    asyncio.run(import_courts())
