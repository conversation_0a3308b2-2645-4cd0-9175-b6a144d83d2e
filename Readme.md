# Setup and Run Instructions

1. Create .env file: Copy the example .env content above and fill in your actual DATABASE_URL and REDIS_URL.

2. Install Dependencies:

```
poetry install
```

3. Initialize Database (if first time): Ensure PostgreSQL is running and the database specified in DATABASE_URL exists.

4. Run Alembic Migrations:

```
# Generate initial migration if no versions folder exists
# poetry run alembic revision --autogenerate -m "Initial schema setup with cases table"

# Apply migrations
poetry run alembic upgrade head
```

5. Run Celery Worker:

```
poetry run celery -A app.worker.celery_app worker --loglevel=info -P prefork
```
(Use -P eventlet or -P gevent for I/O concurrency; -P solo for basic testing)

6. Run Celery Beat (Scheduler):
```
poetry run celery -A app.worker.celery_app beat --loglevel=info

```

7. Run FastAPI App:

```
poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

8. Run Flower UI (Optional):

```
poetry run celery -A app.worker.celery_app flower --loglevel=info --url-prefix=/flower --port=5555
```

9. Seed Token

```
 poetry run python scripts/seed_token.py --count 10 
 ```


10. NGINX

```
server {
    listen 80;
    server_name court-app.startuppp.in;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

11. Nginx Config

sudo cp nginx/ecourt-app /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/ecourt-app /etc/nginx/sites-enabled/

# Test and restart Nginx
sudo nginx -t
sudo systemctl restart nginx

cd /opt/ecourt-app
poetry run alembic upgrade head

12. Logging 
Monitor logs using 
```
journalctl -u ecourt-app
```
