# PostgreSQL Database URL (Adjust credentials/host/db)
DATABASE_URL=postgresql+psycopg://postgres:@localhost:5432/ecourt-app

# Redis URL (Adjust host/port if needed)
REDIS_URL=redis://localhost:6379/0

# External API URLs (Adjust if necessary)
ECOURTS_API_CRN_LIST_URL=https://app.ecourts.gov.in/ecourt_mobile_DC/listOfCasesWebService.php
ECOURTS_API_CRN_HISTORY_URL=https://app.ecourts.gov.in/ecourt_mobile_DC/caseHistoryWebService.php
ECOURTS_AUTH_URL=https://app.ecourts.gov.in/ecourt_mobile_DC/appReleaseWebService.php

# Optional: Override default token pool name or generation count
ECOURTS_API_TOKEN_POOL_KEY=ecourts:token_pool
ECOURTS_TOKENS_TO_GENERATE_PER_RUN=5