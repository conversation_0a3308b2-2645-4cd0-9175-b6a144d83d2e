[Unit]
Description=Celery Beat for Ecourt Application
After=network.target

[Service]
User=ubuntu
Group=ubuntu
WorkingDirectory=/opt/ecourt-fast-app
Environment="PYENV_ROOT=/home/<USER>/.pyenv"
Environment="PATH=/home/<USER>/.pyenv/versions/ecourt-venv/bin:/home/<USER>/.pyenv/bin:/opt/ecourt-app/.venv/bin:$PATH"
ExecStart=/home/<USER>/.pyenv/versions/ecourt-venv/bin/celery -A app.worker.celery_app beat --loglevel=info

[Install]
WantedBy=multi-user.target