[Unit]
Description=FastAPI Ecourt Application
After=network.target

[Service]
User=deployer
Group=deployer
WorkingDirectory=/opt/ecourt-fast-app
Environment="PYENV_ROOT=/home/<USER>/.pyenv"
Environment="PATH=/home/<USER>/.pyenv/versions/ecourt-venv/bin:/home/<USER>/.pyenv/bin:/home/<USER>/.pyenv/shims:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
ExecStart=/home/<USER>/.pyenv/versions/ecourt-venv/bin/uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 --workers 4

Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
/home/<USER>/.pyenv/versions/ecourt-venv
