[Unit]
Description=Celery Worker for Ecourt Application
After=network.target

[Service]
User=deployer
Group=deployer
WorkingDirectory=/opt/ecourt-fast-app
Environment="PYENV_ROOT=/home/<USER>/.pyenv"
Environment="PATH=/home/<USER>/.pyenv/versions/ecourt-venv/bin:/home/<USER>/.pyenv/bin:/home/<USER>/.pyenv/shims:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
ExecStart=/home/<USER>/.pyenv/versions/ecourt-venv/bin/celery -A app.worker.celery_app worker --loglevel=info -P prefork --concurrency=8

[Install]
WantedBy=multi-user.target