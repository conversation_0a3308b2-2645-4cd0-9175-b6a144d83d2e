# app/models/court.py
from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.sql import func
from app.database import Base


class Court(Base):
    __tablename__ = "courts"

    id = Column(Integer, primary_key=True, index=True)
    court_name = Column(String, nullable=False)
    complex_code = Column(Integer, nullable=True)
    court_complex_name = Column(String, nullable=True)
    court_code = Column(Integer, nullable=True)
    national_court_code = Column(String, nullable=True, unique=True)
    district_code = Column(Integer, nullable=True)
    dist_name = Column(String, nullable=True)
    state_code = Column(Integer, nullable=True)
    state_name = Column(String, nullable=True)
    nationalstate_code = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    def __repr__(self):
        return f"<Court(name='{self.court_name}', code='{self.national_court_code}')>"
