# app/config.py
from pydantic_settings import BaseSettings, SettingsConfigDict
import os


class Settings(BaseSettings):
    DATABASE_URL: str
    REDIS_URL: str

    ECOURTS_API_CRN_LIST_URL: str = (
        "https://app.ecourts.gov.in/ecourt_mobile_DC/listOfCasesWebService.php"
    )
    # Settings for the specific external API
    ECOURTS_API_CRN_HISTORY_URL: str = (
        "https://app.ecourts.gov.in/ecourt_mobile_DC/caseHistoryWebService.php"
    )
    ECOURTS_AUTH_URL: str = (
        "https://app.ecourts.gov.in/ecourt_mobile_DC/appReleaseWebService.php"
    )

    # Redis Key for the Set storing available tokens
    ECOURTS_API_TOKEN_POOL_KEY: str = "ecourts:token_pool"
    # Number of tokens the scheduled job attempts to generate each run
    ECOURTS_TOKENS_TO_GENERATE_PER_RUN: int = (
        5  # Example: Generate 5 tokens every 6 hours
    )

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


settings = Settings()
