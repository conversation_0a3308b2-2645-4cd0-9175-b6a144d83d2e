from sqlalchemy.ext.asyncio import AsyncSession
from app.repositories import case_repository
import logging
from typing import Optional
from app.models.case import Case

logger = logging.getLogger(__name__)


class CaseSearchService:
    """Service class for searching and retrieving case data from the database."""

    async def get_case_by_crn(self, db: AsyncSession, crn: str) -> Optional[Case]:
        """
        Retrieve a case by its CRN number from the database.

        Args:
            db (AsyncSession): Database session
            crn (str): Case Reference Number

        Returns:
            Optional[Case]: Case model instance if found, None otherwise
        """
        try:
            case = await case_repository.get_case_by_crn(db, crn)
            if case:
                logger.info(f"Found case in database for CRN: {crn}")
                return case
            logger.info(f"No case found in database for CRN: {crn}")
            return None
        except Exception as e:
            logger.error(f"Error retrieving case for CRN {crn}: {e}")
            raise
