# app/services/data_processing_service.py
from sqlalchemy.ext.asyncio import AsyncSession

# Update imports for new structure
from app.repositories import case_repository
from app import schemas  # Use the __init__.py in schemas
from app.models import case  # Import specific model module
from app.config import settings
from app.utils import api_client
import logging
import redis.asyncio as redis
import secrets
from typing import Tuple, Optional, Dict, Any
import asyncio
import json


from app.utils.court_case_parser import parse_case_data
from app.schemas.case_response_schema import Case

logger = logging.getLogger(__name__)


class DataProcessingService:
    """Encapsulates logic for fetching and storing case data."""

    async def fetch_crn_history_data(self, identifier: str) -> Dict[Any, Any]:
        """Fetches data from the specific external API using the utility."""

        params = {
            "cinum": identifier,
            "language_flag": "english",
            "bilingual_flag": "1",
        }
        api_url = settings.ECOURTS_API_CRN_HISTORY_URL
        try:
            api_response_data = await api_client.call_external_api(
                api_url=api_url, params=params
            )

            logger.info(f"Response data{api_response_data}")
            return api_response_data
        except Exception as e:
            logger.error(
                f"Service: API client error during fetch for {identifier}: {e}"
            )
            raise

    async def _parse_api_response(self, raw_data: Dict[Any, Any]) -> Case:
        """Placeholder for parsing the raw API response data."""
        logger.debug(f"Parsing raw API response data...")
        # --- Placeholder parsing logic ---

        history = raw_data.get("history", {})
        if not history:
            raise ValueError("No history data found in the response.")
        result = await parse_case_data(history)

        return result

    async def process_and_store_identifier(
        self, db: AsyncSession, identifier: str
    ) -> Tuple[str, Optional[case.Case]]:
        """
        Core logic: Checks existence, fetches, parses, and stores case data.
        Returns status string and Case model instance.
        """
        logger.info(f"Service: Processing identifier (CRN): {identifier}")

        # 1. Check existence using repository
        existing_case = await case_repository.get_case_by_crn(db, identifier)
        if existing_case:
            logger.warning(
                f"Service: Case already exists for CRN: {identifier}. Skipping."
            )
            # return "skipped_exists", existing_case

        # 2. Fetch data
        try:
            fetched_dict = await self.fetch_crn_history_data(identifier)
        except Exception as fetch_exc:
            raise fetch_exc  # Propagate error

        # 3. Parse data
        try:

            parsed_dict = await self._parse_api_response(fetched_dict)

        except Exception as parse_exc:
            logger.error(
                f"Service: Failed to parse response for {identifier}: {parse_exc}"
            )
            parsed_dict = {"parsing_error": str(parse_exc)}  # Store error note

        # 4. Prepare schema for creation

        # Check if parsed_dict is a Case object or a dictionary
        # if isinstance(parsed_dict, Case):
        #     parsed_dict = parsed_dict.dict()
        # # Ensure parsed_dict is a dictionary

        case_to_create = schemas.CaseCreate(  # Use imported schema
            crn_no=identifier,
            response_data=fetched_dict,
            parsed_response=parsed_dict.dict(),
        )

        # 5. Store using repository
        try:
            if existing_case:
                updated_case = await case_repository.update_case(
                    db=db, db_case=existing_case, case_update_data=case_to_create
                )
                if not updated_case:
                    logger.error(f"Service: Failed to update case for {identifier}")
                    return "failed_update", None
                logger.info(f"Service: Successfully updated Case for CRN: {identifier}")
                return "success_updated", updated_case
                # Create new case

            # Check if the case already exists in the database
            else:
                logger.info(f"Service: Creating new case for CRN: {identifier}")
                created_case = await case_repository.create_case(
                    db=db, case_data=case_to_create
                )
                if not created_case:
                    logger.error(f"Service: Failed to create case for {identifier}")
                    return "failed_creation", None
            logger.info(f"Service: Successfully stored new Case for CRN: {identifier}")
            return "success_created", created_case
        except Exception as db_exc:
            logger.error(f"Service: Failed during DB store for {identifier}: {db_exc}")
            raise db_exc
