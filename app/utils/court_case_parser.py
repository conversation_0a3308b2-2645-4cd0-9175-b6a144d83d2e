# Parsing Functions
from app.schemas.case_response_schema import *
from bs4 import BeautifulSoup


def parse_under_section(section_str: str) -> List[str]:
    if not section_str:
        return []
    sections = [s.strip() for s in section_str.split(",") if s.strip()]
    return sections


def parse_acts(html_content: str) -> List[Act]:
    if not html_content:
        return []
    soup = BeautifulSoup(html_content, "html.parser")
    acts = []
    for row in soup.find_all("tr")[1:]:  # Skip header
        cols = row.find_all("td")
        if len(cols) == 2:
            under_act = cols[0].text.strip()
            under_section_raw = cols[1].text.strip()
            under_sections = parse_under_section(under_section_raw)
            acts.append(Act(under_act=under_act, under_section=under_sections))
    return acts


def parse_hearings(html_content: str) -> List[Hearing]:
    if not html_content:  # Handle None or empty string
        return []
    soup = BeautifulSoup(html_content, "html.parser")
    hearings = []
    for row in soup.find_all("tr")[1:]:  # Skip header
        cols = row.find_all("td")
        if len(cols) == 4:
            hearings.append(
                Hearing(
                    judge=cols[0].text.strip(),
                    business_date=cols[1].text.strip(),
                    hearing_date=cols[2].text.strip(),
                    purpose=cols[3].text.strip(),
                )
            )
    return hearings


def parse_final_order(html_content: str) -> Optional[Order]:
    if not html_content:
        return None
    soup = BeautifulSoup(html_content, "html.parser")
    row = soup.find("tr")
    if row:
        cols = row.find_all("td")
        if len(cols) >= 3:
            order_number = cols[0].text.strip()
            order_date = cols[1].text.strip()
            order_details = cols[2].text.strip()
            link = cols[2].find("a")
            order_url = link["href"] if link and "href" in link.attrs else None
            return Order(
                order_number=order_number,
                order_date=order_date,
                order_details=order_details,
                order_url=order_url,
            )
    return None


def parse_transfers(html_content: str) -> List[Transfer]:
    if not html_content:
        return []
    soup = BeautifulSoup(html_content, "html.parser")
    transfers = []
    for row in soup.find_all("tr")[1:]:  # Skip header
        cols = row.find_all("td")
        if len(cols) == 3:
            transfers.append(
                Transfer(
                    transfer_date=cols[0].text.strip(),
                    from_court=cols[1].text.strip(),
                    to_court=cols[2].text.strip(),
                )
            )
    return transfers


def parse_processes(html_content: str) -> List[Process]:
    if not html_content:
        return []
    soup = BeautifulSoup(html_content, "html.parser")
    processes = []
    for row in soup.find_all("tr")[1:]:  # Skip header
        cols = row.find_all("td")
        if len(cols) == 3:
            processes.append(
                Process(
                    process_id=cols[0].text.strip(),
                    process_date=cols[1].text.strip(),
                    process_title=cols[2].text.strip(),
                )
            )
    return processes


def parse_additional_parties(html_content: str) -> List[Party]:
    if not html_content:
        return []
    soup = BeautifulSoup(html_content, "html.parser")
    parties = []
    for line in soup.find_all("br"):
        text = line.next_sibling
        if text and isinstance(text, str):
            name = text.strip().split(") ")[-1].strip()
            if name:
                parties.append(Party(name=name))
    return parties


# Async parsing logic
async def parse_case_data(case_json: dict) -> Case:

    primary_petitioner = Party(
        name=case_json.get("pet_name", ""),
        local_name=case_json.get("lpet_name", ""),
        advocate=case_json.get("pet_adv", None),  # Allow None
        local_advocate=case_json.get("lpet_adv", None),  # Allow None
        is_legal_heir=case_json.get("pet_legal_heir", "N"),
    )
    additional_petitioners = parse_additional_parties(case_json.get("str_error", ""))
    petitioners = [primary_petitioner] + additional_petitioners
    primary_respondent = Party(
        name=case_json.get("res_name", ""),
        local_name=case_json.get("lres_name", ""),
        advocate=case_json.get("res_adv", None),  # Allow None
        local_advocate=case_json.get("lres_adv", None),  # Allow None
        is_legal_heir=case_json.get("res_legal_heir", "N"),
    )
    additional_respondents = parse_additional_parties(case_json.get("str_error1", ""))
    respondents = [primary_respondent] + additional_respondents
    acts = parse_acts(case_json.get("act", ""))
    hearings = parse_hearings(case_json.get("historyOfCaseHearing", ""))
    final_order = parse_final_order(case_json.get("finalOrder"))
    transfers = parse_transfers(case_json.get("transfer", ""))
    processes = parse_processes(case_json.get("processes", ""))

    case = Case(
        case_no=case_json.get("case_no", ""),
        filing_date=case_json.get("date_of_filing", ""),
        registration_date=case_json.get("dt_regis", ""),
        court_name=case_json.get("court_name", ""),
        petitioner=petitioners,
        respondent=respondents,
        acts=acts,
        hearings=hearings,
        final_order=final_order,
        transfers=transfers,
        processes=processes,
        disposition=case_json.get("disp_name", ""),
    )

    return case
