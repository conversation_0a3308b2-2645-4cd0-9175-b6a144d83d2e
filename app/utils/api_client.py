# app/utils/api_client.py
import json
import random
import requests  # Using requests for sync calls within threads
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import binascii
import re
import logging
import uuid
from app.config import settings  # Import settings for URLs and keys
import redis.asyncio as redis  # Use async redis
import asyncio  # Needed for to_thread
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import AsyncSessionLocal
from app.models.token import ApiToken
from app import schemas
from app.repositories import token_repository

logger = logging.getLogger(__name__)

# Constants for encryption/decryption (adjust if needed for your specific API)
KEY = bytes.fromhex("4D6251655468576D5A7134743677397A")
DECRYPTION_KEY = bytes.fromhex("3273357638782F413F4428472B4B6250")
IV_OPTIONS = [
    "556A586E32723575",
    "34743777217A2543",
    "413F4428472B4B62",
    "48404D635166546A",
    "614E645267556B58",
    "655368566D597133",
]

# --- Helper Functions for Encryption/Decryption/Parsing ---


def _gen_hex16():
    """Generates a 16-character random hex string."""
    return "".join(random.choices("0123456789abcdef", k=16))


def _get_global_iv():
    """Gets a shuffled IV and its original index."""
    shuffled = random.sample(IV_OPTIONS, len(IV_OPTIONS))
    return shuffled[0], IV_OPTIONS.index(shuffled[0])


def _encrypt_data(data):
    """Encrypts data dictionary for the API call payload."""
    try:
        data_encoded = json.dumps(data)
        global_iv, global_index = _get_global_iv()
        random_iv = _gen_hex16()
        iv = bytes.fromhex(global_iv + random_iv)
        cipher = AES.new(KEY, AES.MODE_CBC, iv)
        encrypted = cipher.encrypt(
            pad(data_encoded.encode(), AES.block_size)
        )  # Specify encoding
        encrypted_data = base64.b64encode(encrypted).decode()  # Specify encoding
        return f"{random_iv}{global_index}{encrypted_data}"
    except Exception as e:
        logger.exception(f"Encryption failed: {e}")
        raise ValueError("Data encryption failed") from e


def _clean_json_string(input_string):
    """Cleans potential issues in decoded JSON strings."""
    if not isinstance(input_string, str):
        return input_string  # Return as-is if not a string
    try:

        def unescape_unicode(match):
            return chr(int(match.group(1), 16))

        input_string = re.sub(r"\\u([0-9a-fA-F]{4})", unescape_unicode, input_string)
        input_string = input_string.replace('\\"', '"').replace("\\'", "'")
        input_string = input_string.replace("\\n", "\n").replace("\\r", "\r")
        input_string = input_string.replace("\\t", "\t").replace("\\/", "/")
        # Remove control characters (ASCII 0-31 except for common whitespace like \n, \r, \t)
        input_string = re.sub(r"[\x00-\x08\x0b\x0c\x0e-\x1f]", "", input_string)
        return input_string
    except Exception as e:
        logger.warning(
            f"Error during JSON string cleaning: {e}. Returning original problematic string."
        )
        return input_string  # Return original if cleaning fails


def _parse_json_safely(input_string):
    """Parses JSON string, attempting cleaning on failure."""
    try:
        return json.loads(input_string)
    except json.JSONDecodeError:
        logger.warning("Initial JSON parsing failed, attempting to clean string.")
        cleaned_string = _clean_json_string(input_string)
        try:
            return json.loads(cleaned_string)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON even after cleaning: {e}")
            logger.debug(f"Problematic string (cleaned): {cleaned_string[:500]}...")
            raise ValueError(f"Invalid JSON response from API: {e}") from e


def _decode_response(result):
    """Decodes and decrypts the raw API response."""
    if not isinstance(result, str) or len(result) < 32:
        logger.error(
            f"Invalid response format for decoding. Length: {len(result)}. Type: {type(result)}"
        )
        raise ValueError("Invalid response format received from API for decoding.")
    try:
        iv_random = bytes.fromhex(result.strip()[:32])
        result_split = result.strip()[32:]
        cipher = AES.new(DECRYPTION_KEY, AES.MODE_CBC, iv_random)
        encrypted_data_bytes = base64.b64decode(result_split.strip())
        decrypted_bytes = cipher.decrypt(encrypted_data_bytes)
        # Use 'ignore' or 'replace' for errors if UTF-8 decoding might fail
        plaintext = unpad(decrypted_bytes, AES.block_size).decode(
            "utf-8", errors="replace"
        )
        s1 = (
            plaintext.replace("\\n", "\\n")
            .replace("\\'", "\\'")
            .replace('\\"', '\\"')
            .replace("\\&", "\\&")
            .replace("\\r", "\\r")
            .replace("\\t", "\\t")
            .replace("\\b", "\\b")
            .replace("\\f", "\\f")
        )
        s1 = re.sub(r"[\x00-\x1F]+", "", s1)
        return _parse_json_safely(s1)
    except (ValueError, binascii.Error, IndexError, TypeError) as e:
        logger.error(f"Failed to decode/decrypt API response: {e}")
        logger.debug(
            f"Problematic response string start: {result[:64]}..."
        )  # Log start
        raise ValueError(f"Error decoding/decrypting API response: {e}") from e


# --- Token Fetching Helper (used ONLY by the GENERATION task) ---


async def fetch_token_with_uuid(uuid_str: str = None) -> tuple[str, str]:
    """
    Fetches a token using a specific UUID or generates a new one.
    Returns a tuple of (uuid, token)
    """
    # Generate a new UUID if none is provided
    if not uuid_str:
        uuid_str = "".join(random.choices("0123456789ABCDEF", k=16))

    pkgname = "in.gov.ecourts.eCourtsServices"  # Ensure this matches API expectation
    data = {"version": "3.0", "uid": f"{uuid_str}:{pkgname}"}
    url = settings.ECOURTS_AUTH_URL

    try:
        encrypted_data = _encrypt_data(data)
        response = requests.get(url, params={"params": encrypted_data}, timeout=30.0)
        response.raise_for_status()

        decrypted_response = _decode_response(response.text)

        if not isinstance(decrypted_response, dict):
            logger.error(
                f"Decoded token response is not a dictionary: {type(decrypted_response)}"
            )
            raise ValueError("Decoded token response is not in expected format.")

        jwt_token = decrypted_response.get("token")
        if not jwt_token:
            logger.error("Fetched response did not contain a 'token' key.")
            raise ValueError("No 'token' found in API auth response")

        logger.info(f"Successfully fetched eCourts API token with UUID: {uuid_str}")
        return uuid_str, jwt_token

    except Exception as e:
        logger.error(f"Error fetching token with UUID {uuid_str}: {e}")
        raise


def fetch_new_ecourts_token_sync() -> str:
    """
    Synchronously fetches ONE new eCourts token.
    Called via asyncio.to_thread by the scheduled generation task.

    This function is maintained for backward compatibility.
    """
    logger.info("Attempting to fetch a new eCourts API token...")
    uuid_str = "".join(random.choices("0123456789ABCDEF", k=16))
    pkgname = "in.gov.ecourts.eCourtsServices"  # Ensure this matches API expectation
    data = {"version": "3.0", "uid": f"{uuid_str}:{pkgname}"}
    url = settings.ECOURTS_AUTH_URL

    try:
        encrypted_data = _encrypt_data(data)
        response = requests.get(
            url, params={"params": encrypted_data}, timeout=30.0
        )  # Timeout for auth call
        response.raise_for_status()  # Check for HTTP errors

        decrypted_response = _decode_response(response.text)

        # Check if response is a dictionary and contains the token
        if not isinstance(decrypted_response, dict):
            logger.error(
                f"Decoded token response is not a dictionary: {type(decrypted_response)}"
            )
            raise ValueError("Decoded token response is not in expected format.")

        jwt_token = decrypted_response.get("token")
        if not jwt_token:
            logger.error("Fetched response did not contain a 'token' key.")
            raise ValueError("No 'token' found in API auth response")

        logger.info("Successfully fetched new eCourts API token.")

        # Store the token in the database asynchronously in a separate task
        # We can't do this directly here because this is a sync function
        asyncio.create_task(store_token_in_db(uuid_str, jwt_token))

        return jwt_token

    except requests.exceptions.Timeout as e:
        logger.error(f"Timeout fetching new eCourts token from {url}: {e}")
        raise TimeoutError(f"Could not fetch API token due to timeout: {e}") from e
    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP error fetching new eCourts token from {url}: {e}")
        raise ConnectionError(
            f"Could not fetch API token due to HTTP error: {e}"
        ) from e
    except (ValueError, TypeError) as e:  # Catch decoding errors or unexpected types
        logger.error(f"Failed to decode or validate token in response from {url}: {e}")
        raise ValueError(f"Error processing token response: {e}") from e
    except Exception as e:
        logger.exception(
            f"An unexpected error occurred while fetching token: {e}"
        )  # Log stack trace
        raise RuntimeError(f"Unexpected error fetching token: {e}") from e


async def store_token_in_db(uuid_str: str, token: str) -> ApiToken:
    """
    Stores a token in the database.
    """
    try:
        async with AsyncSessionLocal() as db:
            # Check if this UUID already exists
            existing_token = await token_repository.get_token_by_uuid(db, uuid_str)

            if existing_token:
                # Update the existing token
                token_data = schemas.TokenUpdate(
                    token=token, is_active=True, last_used_at=None
                )
                updated_token = await token_repository.update_token(
                    db, existing_token.id, token_data
                )
                logger.info(f"Updated existing token with UUID: {uuid_str}")
                return updated_token
            else:
                # Create a new token
                token_data = schemas.TokenCreate(
                    uuid=uuid_str, token=token, is_active=True
                )
                new_token = await token_repository.create_token(db, token_data)
                logger.info(f"Created new token with UUID: {uuid_str}")
                return new_token
    except Exception as e:
        logger.error(f"Error storing token in database: {e}")
        # Don't raise the exception, just log it
        # This is a background task and shouldn't affect the main flow


# --- Token Retrieval from Pool (used by API calls) ---


async def get_api_auth_token() -> str:
    """
    Retrieves an available eCourts JWT token.
    First tries to get a token from the database, then falls back to Redis.
    Raises ValueError if no tokens are available.
    """
    # First try to get a token from the database
    try:
        async with AsyncSessionLocal() as db:
            # Get an active token from the database
            db_token = await token_repository.get_active_token(db)

            if db_token:
                # Mark the token as used
                await token_repository.mark_token_used(db, db_token.id)
                logger.info(f"Retrieved token from database with UUID: {db_token.uuid}")
                return db_token.token
    except Exception as e:
        logger.warning(
            f"Error retrieving token from database: {e}. Falling back to Redis."
        )

    # If no token in database or error occurred, fall back to Redis
    redis_key = settings.ECOURTS_API_TOKEN_POOL_KEY
    r = None
    try:
        logger.debug(f"Attempting to get token from Redis pool: {redis_key}")
        r = await redis.from_url(settings.REDIS_URL, decode_responses=True)
        # Get a random member from the set
        token = await r.srandmember(redis_key, 1)

        if token and isinstance(token, list) and len(token) > 0:
            await r.close()
            logger.info("Retrieved token from Redis pool")
            return token[0]
        else:
            # Pool is empty
            await r.close()
            logger.error(
                f"No tokens available: Neither in database nor in Redis pool '{redis_key}'."
            )
            raise ValueError(
                f"No tokens available. Wait for generation job or increase pool size."
            )

    except redis.RedisError as e:
        logger.error(f"Redis error retrieving token from pool '{redis_key}': {e}")
        if r:
            await r.close()
        raise ConnectionError(f"Redis communication failed: {e}") from e
    except Exception as e:
        logger.error(f"Unexpected error retrieving token: {e}")
        if r:
            await r.close()
        # Wrap unexpected errors
        raise RuntimeError(f"Failed to get API auth token: {e}") from e


# --- Main API Call Function ---


async def call_external_api(api_url: str, params: dict) -> dict:
    """
    Calls the specific external API using encryption and dynamically fetched JWT from pool.
    Handles async context switching for blocking calls.
    """
    logger.info(f"Preparing to call external API: {api_url} for CRN: {params}")
    jwt_token = None  # Initialize
    try:
        # 1. Get a token from the pool (async)
        jwt_token = await get_api_auth_token()

        # 2. Prepare headers and params (sync encryption is generally fast)
        encrypted_params = {"params": _encrypt_data(params)}
        headers = {"Authorization": "Bearer " + _encrypt_data(jwt_token)}

        # 3. Make the actual API call using requests in a thread (sync)
        logger.debug("Dispatching blocking API call to thread...")
        # /fix

        # response = requests.get(api_url, params=encrypted_params, headers=headers)

        response = await asyncio.to_thread(
            requests.get,
            api_url,
            params=encrypted_params,
            headers=headers,
            timeout=45.0,  # Timeout for the data API call itself
        )

        logger.debug(
            f"Received response (status: {response.status_code}) from API thread."
        )

        # 4. Process response
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx/5xx)
        decoded_data = _decode_response(response.text)
        logger.info(
            f"Successfully called and decoded response from {api_url} for params: {params}"
        )
        return decoded_data

    except requests.exceptions.Timeout as e:
        logger.error(
            f"API call timed out for {api_url} (CRN: {params.get('cino', 'N/A')}): {e}"
        )
        raise TimeoutError(f"API call timed out: {e}") from e
    except requests.exceptions.RequestException as e:
        logger.error(
            f"API call HTTP error for {api_url} (CRN: {params.get('cino', 'N/A')}): {e}"
        )
        error_detail = str(e)
        status_code = 500  # Default
        error_content = "No response body"
        if e.response is not None:
            status_code = e.response.status_code
            try:
                response_text = e.response.text
                # Attempt to decode error response only if it seems possible
                if response_text and len(response_text) > 32:
                    error_content = _decode_response(response_text)
                else:
                    error_content = response_text[
                        :500
                    ]  # Show raw error if short/undecodable
            except Exception:
                error_content = e.response.text[:500]  # Fallback to raw
        logger.debug(
            f"API Error Details: Status={status_code}, Content='{error_content}'"
        )
        # Raise a generic connection error, details logged above
        raise ConnectionError(f"API call failed (HTTP {status_code})") from e
    except (
        ValueError
    ) as e:  # Catch decoding/parsing errors or "pool empty" from get_api_auth_token
        logger.error(
            f"Data error during API call/processing for {api_url} (CRN: {params.get('cino', 'N/A')}): {e}"
        )
        raise  # Re-raise value errors (includes pool empty, invalid response format)
    except (
        ConnectionError
    ) as e:  # Catch Redis connection errors from get_api_auth_token
        logger.error(
            f"Infrastructure connection error during API call to {api_url} (CRN: {params.get('cino', 'N/A')}): {e}"
        )
        raise  # Re-raise Redis/token connection errors
    except Exception as e:
        # Catch-all for unexpected errors during the process
        logger.exception(
            f"Unexpected error during call_external_api for {api_url} (CRN: {params.get('cino', 'N/A')}): {e}"
        )
        raise RuntimeError(f"Unexpected API client error: {e}") from e
