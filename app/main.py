# app/main.py
from fastapi import FastAPI

# --- Updated Import Path ---
from app.api.routers import processing  # Import from the 'routers' directory

# --- End Update ---
from app.config import settings
import logging.config
import logging

# Basic logging setup (customize further if needed)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Data Processing API",
    description="API to trigger background data fetching and storage.",
    version="0.1.0",
)

# Include routers (using the imported module)
app.include_router(processing.router, prefix="/api/v1", tags=["Case Processing"])
# Example: app.include_router(users.router, prefix="/api/v1", tags=["Users"]) # If you had a users router


@app.on_event("startup")
async def startup_event():
    logger.info("Application startup...")
    logger.info(
        f"Loaded Settings - API URL: {settings.ECOURTS_API_CRN_HISTORY_URL}, Auth URL: {settings.ECOURTS_AUTH_URL}"
    )
    logger.info(
        f"Loaded Settings - DB URL Prefix: {''.join(settings.DATABASE_URL.split('@')[0])}..."
    )
    logger.info(
        f"Loaded Settings - Redis URL: {settings.REDIS_URL}, Token Pool Key: {settings.ECOURTS_API_TOKEN_POOL_KEY}"
    )
    logger.info("Application started successfully.")


@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Application shutdown...")


@app.get("/", tags=["Health"])
async def read_root():
    """Root endpoint for health check."""
    return {"status": "API is running"}
