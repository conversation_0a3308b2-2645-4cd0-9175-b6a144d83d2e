# app/worker/tasks.py
from .celery_app import celery_app
from app.database import AsyncSessionLocal

# Update service import
from app.services.data_processing_service import DataProcessingService
from app.utils import api_client
from app.config import settings
from app import schemas
import logging
import asyncio
import redis.asyncio as redis
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)


# --- Job 1: iterate_and_trigger_fetch_task ---
@celery_app.task(name="app.worker.tasks.iterate_and_trigger_fetch")
def iterate_and_trigger_fetch_task(
    start_no: int, end_no: int, prefix: str, suffix: str
):
    logger.info(
        f"Task: Starting iteration: {start_no}-{end_no}, prefix={prefix}, suffix={suffix}"
    )
    count = 0
    for i in range(start_no, end_no + 1):
        formatted_no = f"{i:06d}"
        identifier = f"{prefix}{formatted_no}{suffix}"
        logger.info(f"Task: CRN is trigger  fetch_and_store_data {identifier }.")

        fetch_and_store_data_task.delay(identifier)
        count += 1
    logger.info(f"Task: Iteration finished. Triggered {count} fetch tasks.")
    return {"status": "Iteration complete", "triggered_tasks": count}


# --- Job 2: fetch_and_store_data_task ---
@celery_app.task(
    name="app.worker.tasks.fetch_and_store_data",
    bind=True,
    max_retries=3,
    default_retry_delay=60,
)
def fetch_and_store_data_task(self, identifier: str):
    logger.info(f"Task: Received job for identifier: {identifier}")
    try:
        result = asyncio.run(_fetch_and_store(identifier))
        return result
    except Exception as exc:
        if isinstance(exc, ValueError) and "pool empty" in str(exc).lower():
            logger.warning(
                f"Task: Token pool empty for {identifier}. Retrying later..."
            )
            raise self.retry(exc=exc, countdown=300)
        else:
            logger.error(
                f"Task: Error processing identifier {identifier}: {exc}. Retrying..."
            )
            try:
                raise self.retry(exc=exc)
            except self.MaxRetriesExceededError:
                logger.critical(
                    f"Task: Max retries exceeded for identifier {identifier}."
                )
                return {
                    "task_status": "failed",
                    "identifier": identifier,
                    "error": "Max retries exceeded or persistent error",
                }


async def _fetch_and_store(identifier: str):
    db: AsyncSession = AsyncSessionLocal()
    service = DataProcessingService()
    try:
        status, _ = await service.process_and_store_identifier(
            db=db, identifier=identifier
        )
        logger.info(
            f"Task: Service processing finished for {identifier} with status: {status}"
        )
        return {
            "task_status": "completed",
            "service_status": status,
            "identifier": identifier,
        }
    finally:
        await db.close()


# --- Task: Generate eCourts Tokens and Add to Pool ---
@celery_app.task(name="app.worker.tasks.generate_and_add_ecourts_tokens_task")
async def generate_and_add_ecourts_tokens_task():
    num_to_gen = settings.ECOURTS_TOKENS_TO_GENERATE_PER_RUN
    redis_key = settings.ECOURTS_API_TOKEN_POOL_KEY
    logger.info(
        f"Task: Starting token generation. Attempting {num_to_gen} tokens for pool '{redis_key}'."
    )
    tokens_added_to_redis = 0
    tokens_added_to_db = 0
    r = None
    db = None

    try:
        # Connect to Redis for backward compatibility
        r = await redis.from_url(settings.REDIS_URL, decode_responses=True)

        # Connect to database
        db = AsyncSessionLocal()

        # Get existing UUIDs from database to reuse them
        from app.repositories import token_repository

        existing_tokens = await token_repository.get_all_tokens(db)
        existing_uuids = [token.uuid for token in existing_tokens if token.uuid]

        # If we have fewer existing UUIDs than requested count, we'll generate new ones
        uuids_to_use = existing_uuids[:num_to_gen]
        new_uuids_needed = max(0, num_to_gen - len(uuids_to_use))

        logger.info(
            f"Task: Found {len(existing_uuids)} existing UUIDs, will reuse {len(uuids_to_use)} and generate {new_uuids_needed} new ones"
        )

        # First, try to regenerate tokens for existing UUIDs
        for i, uuid_str in enumerate(uuids_to_use):
            try:
                logger.info(
                    f"Task: Regenerating token for existing UUID {i+1}/{len(uuids_to_use)}: {uuid_str}"
                )
                uuid_str, new_token = await api_client.fetch_token_with_uuid(uuid_str)

                # Store in database
                token_data = schemas.TokenUpdate(
                    token=new_token, is_active=True, last_used_at=None
                )
                existing_token = await token_repository.get_token_by_uuid(db, uuid_str)
                await token_repository.update_token(db, existing_token.id, token_data)
                tokens_added_to_db += 1

                # Also add to Redis for backward compatibility
                await r.sadd(redis_key, new_token)
                tokens_added_to_redis += 1

                logger.info(f"Task: Successfully regenerated token for UUID {uuid_str}")
            except Exception as token_error:
                logger.error(
                    f"Task: Failed to regenerate token for UUID {uuid_str}: {token_error}"
                )
                # Mark this token as inactive
                try:
                    await token_repository.deactivate_token(db, uuid_str)
                    logger.info(f"Task: Marked token with UUID {uuid_str} as inactive")
                except Exception as e:
                    logger.error(f"Task: Failed to mark token as inactive: {e}")
                continue

        # Then generate completely new tokens if needed
        for i in range(new_uuids_needed):
            try:
                logger.info(f"Task: Generating new token {i+1}/{new_uuids_needed}")
                uuid_str, new_token = await api_client.fetch_token_with_uuid()

                # Store in database
                token_data = schemas.TokenCreate(
                    uuid=uuid_str, token=new_token, is_active=True
                )
                await token_repository.create_token(db, token_data)
                tokens_added_to_db += 1

                # Also add to Redis for backward compatibility
                await r.sadd(redis_key, new_token)
                tokens_added_to_redis += 1

                logger.info(
                    f"Task: Successfully generated new token with UUID {uuid_str}"
                )
            except Exception as token_error:
                logger.error(f"Task: Failed to generate new token: {token_error}")
                continue

        logger.info(
            f"Task: Token generation finished. Added {tokens_added_to_db} tokens to DB and {tokens_added_to_redis} to Redis pool."
        )
        return {
            "status": "success",
            "tokens_added_to_db": tokens_added_to_db,
            "tokens_added_to_redis": tokens_added_to_redis,
        }
    except Exception as e:
        logger.error(f"Task: Error during token generation task: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        if r:
            await r.close()
        if db:
            await db.close()
