from celery import Celery
from celery.schedules import crontab
from app.config import settings
import logging

logger = logging.getLogger(__name__)

celery_app = Celery(
    "worker",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=["app.worker.tasks"], # Tell Celery where to find tasks
)

# Optional configuration, see the celery docs.
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],  # Ignore other content
    result_serializer='json',
    timezone='Asia/Kolkata', # Adjust to your timezone
    enable_utc=True,
    broker_connection_retry_on_startup=True,
)

# Setup Celery Beat schedule for token generation
celery_app.conf.beat_schedule = {
    'generate-token-every-6-hours': {
        'task': 'app.worker.tasks.generate_and_store_token_task',
        'schedule': crontab(minute=0, hour='*/6'), # Runs at 00:00, 06:00, 12:00, 18:00
        # 'args': (1,), # Example if you need to pass args (e.g., number of tokens)
    },
}

logger.info(f"Celery app configured with broker: {settings.REDIS_URL}")