# app/schemas/__init__.py

# Import schemas from specific files to make them available under app.schemas
from .case_schema import (
    CaseBase,
    CaseCreate,
    CaseUpdate,
    CaseResponse,
    ProcessingRequest,
    ProcessingResponse,
)
from .case_response_schema import *

from .court_schema import (
    CourtBase,
    CourtCreate,
    CourtUpdate,
    CourtResponse,
)
from .token_schema import (
    TokenBase,
    TokenCreate,
    TokenUpdate,
    TokenResponse,
)

# Define __all__ if needed
__all__ = [
    "CaseBase",
    "CaseCreate",
    "CaseUpdate",
    "CaseResponse",
    "ProcessingRequest",
    "ProcessingResponse",
    "TokenBase",
    "TokenCreate",
    "TokenUpdate",
    "TokenResponse",
]
