from typing import List, Optional, Dict, Any


from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime


class Act(BaseModel):
    under_act: str
    under_section: List[str]


class Hearing(BaseModel):
    judge: str
    business_date: str
    hearing_date: str
    purpose: str


class Party(BaseModel):
    name: str
    local_name: Optional[str] = ""
    advocate: Optional[str] = None  # Explicitly allow None
    local_advocate: Optional[str] = None  # Explicitly allow None
    is_legal_heir: str = "N"


class Order(BaseModel):
    order_number: str
    order_date: str
    order_details: str
    order_url: Optional[str] = None


class Transfer(BaseModel):
    transfer_date: str
    from_court: str
    to_court: str


class Process(BaseModel):
    process_id: str
    process_date: str
    process_title: str


class Case(BaseModel):
    case_no: str
    filing_date: str
    registration_date: str
    court_name: str
    petitioner: List[Party]
    respondent: List[Party]
    acts: List[Act]
    hearings: List[Hearing]
    final_order: Optional[Order] = None
    transfers: List[Transfer]
    processes: List[Process]
    disposition: str


# Pydantic model for FastAPI input validation
class CaseInput(BaseModel):
    history: dict
