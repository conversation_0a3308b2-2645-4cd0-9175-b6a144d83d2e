# app/schemas/case_schema.py
from pydantic import BaseModel, Field, field_serializer
from typing import Optional, Dict, Any
from datetime import datetime


# --- Processing Schemas (Can stay here or move to a processing_schema.py) ---
class ProcessingRequest(BaseModel):
    start_no: int = Field(..., gt=0, description="Starting number (positive integer)")
    end_no: int = Field(..., description="Ending number (inclusive)")
    prefix: str = Field(..., min_length=1, description="String prefix")
    suffix: str = Field(..., min_length=1, description="String suffix")

    model_config = {
        "json_schema_extra": {
            "examples": [
                {"start_no": 1, "end_no": 10, "prefix": "MH01", "suffix": "2015"}
            ]
        }
    }


class ProcessingResponse(BaseModel):
    message: str
    task_id: str


# --- Case Schemas ---
class CaseBase(BaseModel):
    crn_no: str = Field(..., description="Unique Case Reference Number")
    response_data: Optional[Dict[Any, Any]] = Field(
        None, description="Raw JSON response from the API"
    )
    parsed_response: Optional[Dict[Any, Any]] = Field(
        None, description="Parsed/structured data from the response"
    )


class CaseCreate(CaseBase):
    pass


class CaseUpdate(CaseBase):
    crn_no: Optional[str] = None
    response_data: Optional[Dict[Any, Any]] = None
    parsed_response: Optional[Dict[Any, Any]] = None


class CaseResponse(CaseBase):
    id: int
    created_at: datetime
    updated_at: datetime

    @field_serializer("created_at", "updated_at")
    def serialize_datetime(self, dt: datetime) -> str:
        return dt.isoformat()

    model_config = {"from_attributes": True}  # Updated for Pydantic v2
