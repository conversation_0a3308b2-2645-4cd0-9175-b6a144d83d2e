# app/schemas/token_schema.py
from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime

class TokenBase(BaseModel):
    uuid: str = Field(..., description="Unique identifier for the token")
    token: str = Field(..., description="The actual JWT token")
    is_active: bool = Field(True, description="Whether the token is active")

class TokenCreate(TokenBase):
    pass

class TokenUpdate(BaseModel):
    token: Optional[str] = None
    is_active: Optional[bool] = None
    last_used_at: Optional[datetime] = None

class TokenResponse(TokenBase):
    id: int
    last_used_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
