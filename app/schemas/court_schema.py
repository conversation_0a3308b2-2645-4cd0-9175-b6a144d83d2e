# app/schemas/court_schema.py
from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime


class CourtBase(BaseModel):
    court_name: str = Field(..., description="Name of the court")
    complex_code: Optional[int] = Field(None, description="Complex code of the court")
    court_complex_name: Optional[str] = Field(
        None, description="Complex name of the court"
    )
    court_code: Optional[int] = Field(None, description="Court code")
    national_court_code: Optional[str] = Field(None, description="National court code")
    district_code: Optional[int] = Field(None, description="District code")
    dist_name: Optional[str] = Field(None, description="District name")
    state_code: Optional[int] = Field(None, description="State code")
    state_name: Optional[str] = Field(None, description="State name")
    nationalstate_code: Optional[str] = Field(None, description="National state code")
    is_active: bool = Field(True, description="Whether the court is active")


class CourtCreate(CourtBase):
    pass


class CourtUpdate(CourtBase):
    court_name: Optional[str] = None
    complex_code: Optional[int] = None
    court_complex_name: Optional[str] = None
    court_code: Optional[int] = None
    national_court_code: Optional[str] = None
    district_code: Optional[int] = None
    dist_name: Optional[str] = None
    state_code: Optional[int] = None
    state_name: Optional[str] = None
    nationalstate_code: Optional[str] = None
    is_active: Optional[bool] = None


class CourtResponse(CourtBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}  # For Pydantic v2
