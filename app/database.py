# app/database.py
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from app.config import settings
import logging
from sqlalchemy_utils import database_exists, create_database

logger = logging.getLogger(__name__)

DATABASE_URL = settings.DATABASE_URL
SYNC_DATABASE_URL = DATABASE_URL.replace("postgresql+psycopg", "postgresql")

if not DATABASE_URL:
    logger.error("DATABASE_URL environment variable not set.")
    raise ValueError("DATABASE_URL environment variable not set.")

# Optional: Ensure database exists
# if not database_exists(SYNC_DATABASE_URL):
#     try:
#         create_database(SYNC_DATABASE_URL)
#         logger.info(f"Database created at {SYNC_DATABASE_URL}")
#     except Exception as e:
#         logger.error(f"Failed to create database: {e}")

try:
    engine = create_async_engine(DATABASE_URL, echo=False, future=True)
    AsyncSessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )
    # Define Base here
    Base = declarative_base()
    logger.info(
        "Database async engine, session maker, and Base configured successfully."
    )
except Exception as e:
    logger.exception(f"Failed to create database async engine: {e}")
    raise


async def get_db() -> AsyncSession:
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
