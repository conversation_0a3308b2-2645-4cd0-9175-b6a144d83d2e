# app/repositories/case_repository.py
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
# Import the specific model and schemas
from app.models.case import Case
from app.schemas import case_schema as schemas # Use alias if needed
import logging

logger = logging.getLogger(__name__)

# --- Case Repository Functions ---

async def create_case(db: AsyncSession, case_data: schemas.CaseCreate) -> Case:
    """Creates a new Case record in the database."""
    db_case = Case( # Use imported Case model
        crn_no=case_data.crn_no,
        response_data=case_data.response_data,
        parsed_response=case_data.parsed_response
    )
    db.add(db_case)
    try:
        await db.commit()
        await db.refresh(db_case)
        logger.info(f"Successfully created Case for crn_no: {case_data.crn_no}")
        return db_case
    except Exception as e:
        await db.rollback()
        logger.error(f"DB Error creating Case for {case_data.crn_no}: {e}")
        raise

async def get_case_by_crn(db: AsyncSession, crn_no: str) -> Case | None:
    """Retrieves a Case record by its unique CRN number."""
    logger.debug(f"Querying DB for case with crn_no: {crn_no}")
    try:
        result = await db.execute(
            select(Case).filter(Case.crn_no == crn_no) # Use imported Case model
        )
        case = result.scalars().first()
        if case:
            logger.debug(f"Found case with crn_no: {crn_no}")
        else:
            logger.debug(f"Case with crn_no: {crn_no} not found.")
        return case
    except Exception as e:
        logger.error(f"DB Error getting Case for {crn_no}: {e}")
        raise

async def update_case(db: AsyncSession, db_case: Case, case_update_data: schemas.CaseUpdate) -> Case:
    """Updates an existing Case record."""
    update_data = case_update_data.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_case, key, value)
    try:
        await db.commit()
        await db.refresh(db_case)
        logger.info(f"Successfully updated Case for crn_no: {db_case.crn_no}")
        return db_case
    except Exception as e:
        await db.rollback()
        logger.error(f"DB Error updating Case for {db_case.crn_no}: {e}")
        raise