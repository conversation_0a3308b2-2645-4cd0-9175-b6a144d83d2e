# app/repositories/court_repository.py
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from app.models.court import Court
from app.schemas import court_schema as schemas
import logging
from typing import List, Optional

logger = logging.getLogger(__name__)

# --- Court Repository Functions ---


async def create_court(db: AsyncSession, court_data: schemas.CourtCreate) -> Court:
    """Creates a new Court record in the database."""
    db_court = Court(
        court_name=court_data.court_name,
        complex_code=court_data.complex_code,
        court_complex_name=court_data.court_complex_name,
        court_code=court_data.court_code,
        national_court_code=court_data.national_court_code,
        district_code=court_data.district_code,
        dist_name=court_data.dist_name,
        state_code=court_data.state_code,
        state_name=court_data.state_name,
        nationalstate_code=court_data.nationalstate_code,
        is_active=court_data.is_active,
    )
    db.add(db_court)
    try:
        await db.commit()
        await db.refresh(db_court)
        logger.info(f"Successfully created Court: {court_data.court_name}")
        return db_court
    except Exception as e:
        await db.rollback()
        logger.error(f"DB Error creating Court {court_data.court_name}: {e}")
        raise


async def get_court_by_id(db: AsyncSession, court_id: int) -> Optional[Court]:
    """Retrieves a Court record by its ID."""
    try:
        result = await db.execute(select(Court).filter(Court.id == court_id))
        return result.scalars().first()
    except Exception as e:
        logger.error(f"DB Error getting Court with ID {court_id}: {e}")
        raise


async def get_court_by_national_code(db: AsyncSession, code: str) -> Optional[Court]:
    """Retrieves a Court record by its national court code."""
    try:
        result = await db.execute(
            select(Court).filter(Court.national_court_code == code)
        )
        return result.scalars().first()
    except Exception as e:
        logger.error(f"DB Error getting Court with national code {code}: {e}")
        raise


async def get_courts(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Court]:
    """Retrieves a list of Court records."""
    try:
        result = await db.execute(select(Court).offset(skip).limit(limit))
        return result.scalars().all()
    except Exception as e:
        logger.error(f"DB Error getting Courts: {e}")
        raise


async def update_court(
    db: AsyncSession, db_court: Court, court_update_data: schemas.CourtUpdate
) -> Court:
    """Updates an existing Court record."""
    update_data = court_update_data.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_court, key, value)
    try:
        await db.commit()
        await db.refresh(db_court)
        logger.info(f"Successfully updated Court: {db_court.court_name}")
        return db_court
    except Exception as e:
        await db.rollback()
        logger.error(f"DB Error updating Court {db_court.court_name}: {e}")
        raise
