# app/repositories/token_repository.py
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, and_
from app.models.token import ApiToken
from app import schemas
import logging
from datetime import datetime
from typing import List

logger = logging.getLogger(__name__)


async def create_token(db: AsyncSession, token_data: schemas.TokenCreate) -> ApiToken:
    """Creates a new ApiToken record in the database."""
    db_token = ApiToken(
        uuid=token_data.uuid, token=token_data.token, is_active=token_data.is_active
    )
    db.add(db_token)
    try:
        await db.commit()
        await db.refresh(db_token)
        logger.info(f"Successfully created ApiToken for uuid: {token_data.uuid}")
        return db_token
    except Exception as e:
        await db.rollback()
        logger.error(f"DB Error creating ApiToken for {token_data.uuid}: {e}")
        raise


async def get_token_by_uuid(db: AsyncSession, uuid: str) -> ApiToken:
    """Retrieves an ApiToken by its UUID."""
    result = await db.execute(select(ApiToken).where(ApiToken.uuid == uuid))
    return result.scalars().first()


async def get_active_token(db: AsyncSession) -> ApiToken:
    """Retrieves a random active ApiToken."""
    result = await db.execute(
        select(ApiToken)
        .where(ApiToken.is_active == True)
        .order_by(ApiToken.last_used_at.nullsfirst())
    )
    return result.scalars().first()


async def update_token(
    db: AsyncSession, token_id: int, token_data: schemas.TokenUpdate
) -> ApiToken:
    """Updates an existing ApiToken."""
    update_data = token_data.model_dump(exclude_unset=True)

    try:
        await db.execute(
            update(ApiToken).where(ApiToken.id == token_id).values(**update_data)
        )
        await db.commit()

        # Fetch the updated token
        result = await db.execute(select(ApiToken).where(ApiToken.id == token_id))
        updated_token = result.scalars().first()
        logger.info(f"Successfully updated ApiToken with id: {token_id}")
        return updated_token
    except Exception as e:
        await db.rollback()
        logger.error(f"DB Error updating ApiToken with id {token_id}: {e}")
        raise


async def mark_token_used(db: AsyncSession, token_id: int) -> ApiToken:
    """Updates the last_used_at timestamp for a token."""
    try:
        await db.execute(
            update(ApiToken)
            .where(ApiToken.id == token_id)
            .values(last_used_at=datetime.now())
        )
        await db.commit()

        # Fetch the updated token
        result = await db.execute(select(ApiToken).where(ApiToken.id == token_id))
        updated_token = result.scalars().first()
        return updated_token
    except Exception as e:
        await db.rollback()
        logger.error(
            f"DB Error updating last_used_at for ApiToken with id {token_id}: {e}"
        )
        raise


async def deactivate_token(db: AsyncSession, uuid: str) -> ApiToken:
    """Marks a token as inactive."""
    try:
        await db.execute(
            update(ApiToken).where(ApiToken.uuid == uuid).values(is_active=False)
        )
        await db.commit()

        # Fetch the updated token
        result = await db.execute(select(ApiToken).where(ApiToken.uuid == uuid))
        updated_token = result.scalars().first()
        logger.info(f"Successfully deactivated ApiToken with uuid: {uuid}")
        return updated_token
    except Exception as e:
        await db.rollback()
        logger.error(f"DB Error deactivating ApiToken with uuid {uuid}: {e}")
        raise


async def get_all_tokens(db: AsyncSession) -> List[ApiToken]:
    """Retrieves all tokens from the database."""
    try:
        result = await db.execute(select(ApiToken))
        tokens = result.scalars().all()
        return tokens
    except Exception as e:
        logger.error(f"DB Error retrieving all tokens: {e}")
        raise
