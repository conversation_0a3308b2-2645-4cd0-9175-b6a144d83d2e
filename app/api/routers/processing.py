# app/api/routers/processing.py
# (Content is exactly the same as app/api/endpoints/processing.py from the previous step)
from fastapi import APIRouter, HTTPException, status, Depends, Path
from sqlalchemy.ext.asyncio import AsyncSession
from app import schemas
from app.worker.tasks import iterate_and_trigger_fetch_task
from app.services.data_processing_service import DataProcessingService
from app.database import get_db
import logging
from fastapi.responses import JSONResponse
import redis.asyncio as redis
import asyncio
from app.utils.api_client import (
    fetch_new_ecourts_token_sync,
    fetch_token_with_uuid,
    store_token_in_db,
)
from app.config import settings
from app.repositories import token_repository

logger = logging.getLogger(__name__)
router = APIRouter()  # Create the router instance here


@router.post(
    "/process-range",
    response_model=schemas.ProcessingResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Start background processing for a range of CRNs",
    description="Triggers a background task to iterate through a number range, format CRNs, fetch case data, parse it, and store it.",
)
async def start_range_processing(
    request: schemas.ProcessingRequest,
):
    if request.start_no > request.end_no:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Start number cannot be greater than end number.",
        )
    logger.info(f"API: Received range processing request: {request.model_dump()}")
    try:
        task = iterate_and_trigger_fetch_task.delay(
            start_no=request.start_no,
            end_no=request.end_no,
            prefix=request.prefix,
            suffix=request.suffix,
        )
        logger.info(f"API: Celery task {task.id} dispatched for range processing.")
        return schemas.ProcessingResponse(
            message="Range processing started in the background.", task_id=task.id
        )
    except Exception as e:
        logger.error(f"API: Failed to dispatch Celery task for range processing: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start background range processing task.",
        )


@router.post(
    "/process-single/{crn_no}",
    response_model=schemas.CaseResponse,
    status_code=status.HTTP_200_OK,
    summary="Process a single Case Reference Number (CRN) immediately",
    description="Fetches data for a single CRN, parses it, and stores it in the database synchronously.",
    responses={
        200: {"description": "Successfully found existing case."},
        201: {"description": "Successfully created new case."},
        400: {"description": "Bad Request (e.g., invalid CRN format)."},
        404: {"description": "CRN not found by external API (if detectable)."},
        500: {"description": "Internal server error."},
        502: {"description": "Bad Gateway - Error communicating with external API."},
        503: {"description": "Service Unavailable - Token pool likely empty."},
        504: {"description": "Gateway Timeout - External API timed out."},
    },
)
async def process_single_crn(crn_no: str, db: AsyncSession = Depends(get_db)):
    logger.info(f"API: Received single processing request for CRN: {crn_no}")
    try:
        service = DataProcessingService()
        status_code, result_case = await service.process_and_store_identifier(
            db=db, identifier=crn_no
        )
        logger.info(
            f"API: Service finished processing CRN {crn_no} with status: {status_code}"
        )

        if result_case is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Processing failed unexpectedly.",
            )

        response_http_status_code = status.HTTP_200_OK
        if status_code == "success_created":
            response_http_status_code = status.HTTP_201_CREATED
        if status_code == "success_created":
            response_http_status_code = status.HTTP_200_OK

        validated_response = schemas.CaseResponse.model_validate(result_case)
        logger.info(
            f"API: Service finished Validated CRN {crn_no} with status: {response_http_status_code}"
        )
        return JSONResponse(
            content=validated_response.model_dump(),
            status_code=response_http_status_code,
        )

    except ValueError as e:
        logger.error(f"API: Value error processing CRN {crn_no}: {e}")
        detail_msg = str(e)
        status_code_err = (
            status.HTTP_400_BAD_REQUEST
        )  # Default for general value errors
        if "pool empty" in detail_msg.lower():
            status_code_err = status.HTTP_503_SERVICE_UNAVAILABLE
        elif "parsing failed" in detail_msg.lower() or "Invalid JSON" in detail_msg:
            status_code_err = (
                status.HTTP_422_UNPROCESSABLE_ENTITY
            )  # Or 500 if internal parsing logic fails
        raise HTTPException(status_code=status_code_err, detail=detail_msg)
    except ConnectionError as e:
        logger.error(f"API: Connection error processing CRN {crn_no}: {e}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"External API connection error: {e}",
        )
    except TimeoutError as e:
        logger.error(f"API: Timeout error processing CRN {crn_no}: {e}")
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail=f"External API timeout: {e}",
        )
    except Exception as e:
        logger.exception(f"API: Unhandled error processing single CRN {crn_no}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process CRN due to an internal error.",
        )


from app.services.case_search_service import CaseSearchService


@router.get(
    "/cases/{crn_no}",
    response_model=schemas.CaseResponse,
    status_code=status.HTTP_200_OK,
    summary="Fetch case data from database",
    description="Retrieves case data for a given CRN number from the database without making external API calls.",
)
async def get_case_by_crn(crn_no: str, db: AsyncSession = Depends(get_db)):
    logger.info(f"API: Received database lookup request for CRN: {crn_no}")
    try:
        search_service = CaseSearchService()
        case = await search_service.get_case_by_crn(db=db, crn=crn_no)

        if case is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Case with CRN {crn_no} not found in database",
            )

        validated_response = schemas.CaseResponse.model_validate(case)
        return JSONResponse(
            content=validated_response.model_dump(),
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        logger.exception(f"API: Error fetching case data for CRN {crn_no}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch case data due to an internal error.",
        )


@router.post(
    "/generate-tokens/{count}",
    status_code=status.HTTP_200_OK,
    summary="Generate multiple new eCourts tokens",
    description="Generates specified number of new tokens and stores them in the database and Redis.",
    responses={
        200: {"description": "Successfully generated new tokens"},
        400: {"description": "Invalid count parameter"},
        500: {"description": "Internal server error"},
        502: {"description": "Failed to generate tokens from external API"},
    },
)
async def generate_new_tokens(
    count: int = Path(
        ..., gt=0, le=50, description="Number of tokens to generate (max 50)"
    ),
    db: AsyncSession = Depends(get_db),
):
    logger.info(f"API: Received request to generate {count} new tokens")

    tokens_generated = 0
    failed_attempts = 0
    db_tokens_stored = 0
    redis_tokens_stored = 0

    try:
        # Connect to Redis
        r = await redis.from_url(settings.REDIS_URL, decode_responses=True)

        try:
            # Get existing UUIDs from database to reuse them
            existing_tokens = await token_repository.get_all_tokens(db)
            existing_uuids = [token.uuid for token in existing_tokens if token.uuid]

            # If we have fewer existing UUIDs than requested count, we'll generate new ones
            uuids_to_use = existing_uuids[:count]
            new_uuids_needed = max(0, count - len(uuids_to_use))

            logger.info(
                f"API: Found {len(existing_uuids)} existing UUIDs, will reuse {len(uuids_to_use)} and generate {new_uuids_needed} new ones"
            )

            # Clear Redis token pool
            deleted = await r.delete(settings.ECOURTS_API_TOKEN_POOL_KEY)
            logger.info(f"API: Deleted {deleted} existing tokens from Redis pool")

            # First, try to regenerate tokens for existing UUIDs
            for i, uuid_str in enumerate(uuids_to_use):
                try:
                    logger.info(
                        f"API: Regenerating token for existing UUID {i+1}/{len(uuids_to_use)}: {uuid_str}"
                    )
                    uuid_str, new_token = await fetch_token_with_uuid(uuid_str)

                    # Store in database
                    token_data = schemas.TokenUpdate(
                        token=new_token, is_active=True, last_used_at=None
                    )
                    existing_token = await token_repository.get_token_by_uuid(
                        db, uuid_str
                    )
                    await token_repository.update_token(
                        db, existing_token.id, token_data
                    )
                    db_tokens_stored += 1

                    # Also add to Redis for backward compatibility
                    await r.sadd(settings.ECOURTS_API_TOKEN_POOL_KEY, new_token)
                    redis_tokens_stored += 1

                    tokens_generated += 1
                    logger.info(
                        f"API: Successfully regenerated token for UUID {uuid_str}"
                    )
                except Exception as token_error:
                    failed_attempts += 1
                    logger.error(
                        f"API: Failed to regenerate token for UUID {uuid_str}: {token_error}"
                    )
                    # Mark this token as inactive
                    try:
                        await token_repository.deactivate_token(db, uuid_str)
                        logger.info(
                            f"API: Marked token with UUID {uuid_str} as inactive"
                        )
                    except Exception as e:
                        logger.error(f"API: Failed to mark token as inactive: {e}")
                    continue

            # Then generate completely new tokens if needed
            for i in range(new_uuids_needed):
                try:
                    logger.info(f"API: Generating new token {i+1}/{new_uuids_needed}")
                    uuid_str, new_token = await fetch_token_with_uuid()

                    # Store in database
                    token_data = schemas.TokenCreate(
                        uuid=uuid_str, token=new_token, is_active=True
                    )
                    await token_repository.create_token(db, token_data)
                    db_tokens_stored += 1

                    # Also add to Redis for backward compatibility
                    await r.sadd(settings.ECOURTS_API_TOKEN_POOL_KEY, new_token)
                    redis_tokens_stored += 1

                    tokens_generated += 1
                    logger.info(
                        f"API: Successfully generated new token with UUID {uuid_str}"
                    )
                except Exception as token_error:
                    failed_attempts += 1
                    logger.error(f"API: Failed to generate new token: {token_error}")
                    continue

            await r.close()

            # Prepare response based on results
            if tokens_generated == 0:
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail="Failed to generate any tokens",
                )

            response_status = (
                status.HTTP_200_OK
                if failed_attempts == 0
                else status.HTTP_207_MULTI_STATUS
            )

            return JSONResponse(
                content={
                    "message": "Token generation completed",
                    "tokens_generated": tokens_generated,
                    "tokens_stored_in_db": db_tokens_stored,
                    "tokens_stored_in_redis": redis_tokens_stored,
                    "failed_attempts": failed_attempts,
                    "total_requested": count,
                },
                status_code=response_status,
            )

        except Exception as e:
            if r:
                await r.close()
            raise e

    except redis.RedisError as e:
        logger.error(f"API: Redis error during token generation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Redis operation failed: {str(e)}",
        )
    except ValueError as e:
        logger.error(f"API: Value error during token generation: {e}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Failed to generate tokens: {str(e)}",
        )
    except Exception as e:
        logger.exception(f"API: Unexpected error during token generation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate tokens due to an internal error",
        )
